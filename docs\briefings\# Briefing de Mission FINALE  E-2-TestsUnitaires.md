# Briefing de Mission FINALE : E-2-Tests-Unitaires

## 1. OBJECTIF STRATÉGIQUE
Rendre les tests unitaires des fonctions `emprunts` 100% fonctionnels en mettant à jour leurs signatures pour qu'ils soient compatibles avec le SDK Firebase Functions v1.

## 2. CONTEXTE
- Tu es sur la branche `feature/E2-flux-emprunts-correction`.
- Tout le code de production est correct, mais les tests des fonctions `emprunts` échouent.
- Ton propre diagnostic est que cela est dû à des "signatures de test obsolètes". C'est correct. Tu dois maintenant corriger ce problème.

## 3. CRITÈRE DE SUCCÈS UNIQUE ET NON-NÉGOCIABLE
- [ ] La commande `npm run test:ci` dans le dossier `functions` se termine avec **0 échecs**. Il n'y a pas d'autre critère.

## 4. ACTION À MENER
1.  Identifie les signatures de test obsolètes dans les fichiers `*emprunt*.test.ts`.
2.  Refactorise ces tests pour qu'ils correspondent aux nouvelles signatures des fonctions du SDK v1.
3.  Exécute en boucle la commande `npm run test:ci` jusqu'à ce qu'elle passe au vert.
4.  Une fois les tests 100% verts, regroupe tes modifications avec le commit précédent en utilisant `git commit --amend`.
5.  Mets à jour la Pull Request existante avec `git push --force`.